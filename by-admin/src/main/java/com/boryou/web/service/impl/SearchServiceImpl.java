package com.boryou.web.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.PageResult;
import cn.hutool.http.HtmlUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.boryou.common.core.domain.entity.SysDictData;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.core.redis.RedisCache;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.DictUtils;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.StringUtils;
import com.boryou.system.service.ISysDictDataService;
import com.boryou.utils.AreaUtil;
import com.boryou.web.constant.RedisConstant;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.controller.common.enums.MediaTypeEnum;
import com.boryou.web.controller.common.enums.SortTypeEnum;
import com.boryou.web.controller.common.util.ConvertHandler;
import com.boryou.web.controller.common.util.EsSearchUtil;
import com.boryou.web.domain.Hot;
import com.boryou.web.domain.Plan;
import com.boryou.web.domain.PushContacts;
import com.boryou.web.domain.msg.MessageVO;
import com.boryou.web.domain.vo.AccountInfoVO;
import com.boryou.web.domain.vo.EsBeanVO;
import com.boryou.web.domain.vo.HomeDownVO;
import com.boryou.web.domain.vo.SearchVO;
import com.boryou.web.mapper.HotListMapper;
import com.boryou.web.module.home.service.impl.HomeStatisServiceImpl;
import com.boryou.web.module.message.domain.MessageTemplate;
import com.boryou.web.module.message.domain.vo.DeptMessage;
import com.boryou.web.module.message.service.IMessageTemplateService;
import com.boryou.web.module.search.entity.EsReadData;
import com.boryou.web.module.search.entity.EsSpecialData;
import com.boryou.web.module.search.service.EsReadDataService;
import com.boryou.web.module.search.service.EsSpecialDataService;
import com.boryou.web.module.warn.domain.vo.WarnDataVO;
import com.boryou.web.module.warn.service.WarnDataApiService;
import com.boryou.web.module.warn.service.WarnReadService;
import com.boryou.web.service.*;
import com.boryou.web.util.HighlightUtil;
import com.boryou.web.util.SearchUtil;
import com.boryou.web.util.UrlAccessUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.boryou.web.constant.ESConstant.*;
import static com.boryou.web.controller.common.enums.SortTypeEnum.getEnumValue;
import static com.boryou.web.task.CommonDataTask.COURT_NAME_CODE_MAPS;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SearchServiceImpl implements SearchService {
    @Resource
    EsSpecialDataService esSpecialDataService;
    @Resource
    EsReadDataService esReadDataService;
    @Resource
    HotService hotService;
    @Resource
    ElasticsearchService elasticsearchService;

    private final RedisCache redisTemplate;

    private final IPushContactsService pushContactsService;
    @Resource
    private PlanService planService;
    @Resource
    private ConvertHandler convertHandler;

    private final ISysDictDataService dictDataService;

    private final SimilarityService similarityService;

    private final WarnDataApiService warnDataApiService;

    private final IMessageTemplateService messageTemplateService;
    @Resource
    private HotListMapper hotListMapper;

    @Resource
    private WarnReadService warnReadService;

    @Resource(name = "threadPoolTaskExecutor")
    ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    public PageResult<EsBean> search(EsSearchBO bo) {
        long start = System.currentTimeMillis();
        PageResult<EsBean> pageResult = EsSearchUtil.searchEsBeanList(bo);
        long end = System.currentTimeMillis();
        double passedTime = (end - start) / 1000.0;
        long start1 = System.currentTimeMillis();
        searchResProcess(bo, pageResult);
        if (passedTime > 0.2) {
            long end1 = System.currentTimeMillis();
            double passedTime1 = (end1 - start1) / 1000.0;
            log.warn("博约舆情pt-serv纯搜索耗时{}秒,业务耗时:{}秒", passedTime, passedTime1);
        }
        return pageResult;
    }

    public void searchResProcess(EsSearchBO bo, PageResult<EsBean> pageResult) {
        if (CollUtil.isEmpty(pageResult)) {
            return;
        }
        Integer warnType = bo.getWarnType();
        List<String> indexList;
        if (warnType != null && warnType == 1) {
            indexList = pageResult.stream().map(EsBean::getArticleId).collect(Collectors.toList());
        } else {
            indexList = pageResult.stream().map(EsBean::getId).collect(Collectors.toList());
        }

        List<String> md5List = pageResult.stream().map(EsBean::getMd5).collect(Collectors.toList());
//        List<String> host = pageResult.stream().map(EsBean::getHost).filter(s -> s != null && !s.isEmpty()).collect(Collectors.toList());
        Map<Integer, List<String>> hostMap
                = pageResult.stream().filter(s -> s != null && !s.getHost().isEmpty()).collect(Collectors.groupingBy(EsBean::getType, Collectors.mapping(EsBean::getHost, Collectors.toList())));

        List<EsSpecialData> esSpecialDataList;
        Map<String, EsSpecialData> md5Special = MapUtil.newHashMap();
        //已预警id
        List<String> warnIdList = new ArrayList<>();
        List<String> indexRead = new ArrayList<>();
        Authentication authentication = SecurityUtils.getAuthentication();
        boolean isAuthentication = false;
        if (null != authentication) {
            isAuthentication = !authentication.getName().contains("anonymousUser");
        }
        if (isAuthentication) {
            esSpecialDataList = esSpecialDataService.getEsSpecialDatasByList(indexList, md5List);
//              Map<String, EsSpecialData> indexSpecial = esSpecialDataList.stream().collect(Collectors.toMap(d -> String.valueOf(d.getIndexId()), Function.identity()));
            md5Special = esSpecialDataList.stream().collect(Collectors.toMap(EsSpecialData::getMd5, Function.identity(), (oldValue, newValue) -> oldValue, HashMap::new));
            if (warnType != null && warnType == 1) {
                indexRead = warnReadService.getWarnRead();
            } else {
                List<EsReadData> esReadDataList = esReadDataService.getEsReadDatasByList(indexList);
                indexRead = CollStreamUtil.toList(esReadDataList, item -> String.valueOf(item.getIndexId()));
            }
            if (CollUtil.isNotEmpty(indexList)) {
                //查询已预警
                try {
                    String userId = SecurityUtils.getUserId();
                    WarnDataVO warnDataVO = new WarnDataVO();
                    warnDataVO.setUsers(CollUtil.newArrayList(userId));
                    warnDataVO.setArticleIds(indexList);
                    warnIdList = warnDataApiService.warnDataCheck(warnDataVO);
                } catch (Exception e) {
                    log.error("searchResProcess 查询已预警失败: {}", e.getMessage());
                }
            }

        }
        Map<String, String> hostAndNameMap = new HashMap<>();
        if (warnType == null || warnType != 1) {
//            List<AccountInfoVO> accountInfoVOList = elasticsearchService.getNameByHosts(host);
            List<AccountInfoVO> accountInfoVOList = elasticsearchService.getNameByHosts(hostMap);
            if (CollUtil.isNotEmpty(accountInfoVOList)) {
                hostAndNameMap = accountInfoVOList.stream().filter(item -> CharSequenceUtil.isNotBlank(item.getSector()) && CharSequenceUtil.isNotBlank(item.getDomain()))
                        .distinct().collect(Collectors.toMap(AccountInfoVO::getDomain, AccountInfoVO::getSector, (oldValue, newValue) -> oldValue));
            }
//            for (AccountInfoVO accountInfoVO : accountInfoVOList) {
//                hostAndNameMap.putIfAbsent(accountInfoVO.getDomain(),accountInfoVO.getSector());
//            }
        }
        String keyWord = "";
        String keywordBak = "";
        String keyWord1 = "";
        String[] split = {};
        boolean containsType = false;
        if (warnType == null || warnType != 1) {
            //第一类type关键词
            keyWord = SearchUtil.getSearchKeyWord(bo);
            keywordBak = keyWord;
            //第二类type关键词
            keyWord1 = SearchUtil.getSearchKeyWordPro(bo);
            split = bo.getType().split("&");
            containsType = bo.getType().contains("&");
        }
        List<String> courtNames = hotService.selectCourtNames();
//        String keyWord = getSearchKeyWord(bo);
        List<String> areaCodes = new ArrayList<>();
        List<String> areaCodesName = new ArrayList<>();
        if (StrUtil.isNotEmpty(bo.getContentAreaCode())) {
            areaCodes = Arrays.asList(bo.getContentAreaCode().split(","));
            for (String areaCode : areaCodes) {
                areaCodesName.add(AreaUtil.codeNameMap.get(areaCode));
            }
        }
        for (EsBean bean : pageResult) {
//            threadPoolTaskExecutor.submit(() -> cacheState(bean.getId(), bean.getUrl()));
            if (isAuthentication) {
                //收藏  重复
                if (md5Special.containsKey(bean.getMd5())) {
                    //情感
                    if (md5Special.get(bean.getMd5()).getEmotionFlag() != bean.getEmotionFlag()) {
                        bean.setEmotionFlag(md5Special.get(bean.getMd5()).getEmotionFlag());
                    }
                    //噪音
                    if (md5Special.get(bean.getMd5()).getTrash() > 0) {
                        bean.setIsSpam(md5Special.get(bean.getMd5()).getTrash() == 2);
                    }
                    //处置
                    bean.setDeal(md5Special.get(bean.getMd5()).getDeal());
                    //关注
                    bean.setFollow(md5Special.get(bean.getMd5()).getFollow());
                    //流转中
                    bean.setWarned(md5Special.get(bean.getMd5()).getWarned());
                }
                //已预警
                this.setSubmit(bean, warnType, warnIdList);
                //已读
                if (indexRead.contains(bean.getId())) {
                    bean.setIsRead(1);
                    bean.setReadFlag(1);
                }
            }

            List<String> contentAreaCode = bean.getContentAreaCode();
            bean.setContentAreaCodeName(buildArea(contentAreaCode, areaCodes));

            List<String> hitWord = bean.getHitWord();
            if (warnType == null || warnType != 1) {
                keyWord = keywordBak;//重置keyWord1混杂后的keyword
                if (containsType && split.length > 1 && StrUtil.isNotBlank(split[1]) && split[1].contains(String.valueOf(bean.getType()))) {
                    keyWord = keyWord1;
                }
            }
            if (warnType != null && warnType == 1 && CollUtil.isNotEmpty(hitWord)) {
                keyWord = CollUtil.join(hitWord, " ");
            }
            markKeywords(keyWord, bean, courtNames, hostAndNameMap, areaCodesName);
        }
//            executor.shutdown();

    }

    private void setSubmit(EsBean bean, Integer warnType, List<String> warnIdList) {
        String id;
        if (warnType != null && warnType == 1) {
            id = bean.getArticleId();
        } else {
            id = bean.getId();
        }
        int sub = warnIdList.contains(id) ? 1 : 0;
        bean.setSubmits(sub);
    }

    private String buildArea(List<String> contentAreaCode, List<String> areaCodes) {
        //地域
        if (CollUtil.isNotEmpty(contentAreaCode)) {
            Set<String> areaCodeNames = new LinkedHashSet<>();
            Set<String> ignore = new LinkedHashSet<>();
            for (String s : contentAreaCode) {
                if ("999999".equals(s)) {
                    areaCodeNames.add("其他");
                    continue;
                }
                //TOD 这段代码有bug,如果精准地域只有一条，就只会显示一条了
//                if (CollUtil.isEmpty(areaCodes)) {
//                    areaCodeNames.add(areaNameCodeMap.inverse().get(s));
//                    break;
//                }
                String codeName = AreaUtil.codeNameMap.get(s);
                //TODO 临时移除null值
                if (codeName == null) {
                    continue;
                }
                for (String areaCode : areaCodes) {
                    if (StrUtil.isNotEmpty(areaCode)) {
                        if (areaCode.endsWith("00")) {
                            areaCode = areaCode.substring(0, areaCode.length() - 2);
                        }
                        if (areaCode.endsWith("00")) {
                            areaCode = areaCode.substring(0, areaCode.length() - 2);
                        }
                        if (s.startsWith(areaCode)) {
                            areaCodeNames.add(codeName);
                            break;
                        }
                    }
                }
//                if (StrUtil.isEmpty(codeName)){
//                    System.out.println("----");//只取了区及，街道没要，所以为null
//                }
                if (!areaCodeNames.contains(codeName)) {
                    ignore.add(codeName);
                }
            }
            areaCodeNames.addAll(ignore);
            return CollUtil.join(areaCodeNames, " ");
        }
        return "";
    }

    public static String getSearchKeyWord(EsSearchBO bo) {
        //关键词标红
        String keyWord = "";
        if (bo.getConfigSelect() == 1 && StrUtil.isNotEmpty(bo.getProWord())) {
            keyWord = bo.getProWord().replaceAll("\\|", " ").replaceAll("\\+", " ")
                    .replaceAll("\\(", " ").replaceAll("\\)", " ");
            if (StrUtil.isNotEmpty(bo.getQuadraticWord())) {
                keyWord = keyWord + " " + bo.getQuadraticWord();
            }
        } else {
            if (StrUtil.isNotEmpty(bo.getKeyWord1())) {
                keyWord += bo.getKeyWord1() + " ";
            }
            if (StrUtil.isNotEmpty(bo.getKeyWord2())) {
                keyWord += bo.getKeyWord2() + " ";
            }
            if (StrUtil.isNotEmpty(bo.getKeyWord3())) {
                keyWord += bo.getKeyWord3() + " ";
            }
            if (StrUtil.isNotEmpty(bo.getKeyWord4())) {
                keyWord += bo.getKeyWord4() + " ";
            }
            if (StrUtil.isNotEmpty(keyWord)) {
                keyWord = keyWord.substring(0, keyWord.length() - 1);
            }
        }
        return keyWord;
    }

    public static void markKeywords(String keyWord, EsBean bean, List<String> courtNames, Map<String, String> hostAndNameMap, List<String> areaCodeName) {
        //媒体类型
        bean.setTypeName(MediaTypeEnum.getDesc(String.valueOf(bean.getType())));
        //站点名称
        if (hostAndNameMap.containsKey(bean.getHost())) {
            //双微显示作者
            if (bean.getType() == MediaTypeEnum.WECHAT.getValue() || bean.getType() == MediaTypeEnum.WEIBO.getValue()) {
                bean.setHost(bean.getAuthor());
            } else {
                bean.setHost(hostAndNameMap.get(bean.getHost()));
            }
        }
        bean.setDomain(bean.getHost());
        String title = HtmlUtil.cleanHtmlTag(bean.getTitle());
        String text = HtmlUtil.cleanHtmlTag(bean.getText());
        if (!text.startsWith(title)) {
            bean.setRealTitle(title);
        } else {
            bean.setRealTitle("");
        }
        //法院名称优先打标
        StringBuilder builder = new StringBuilder();
        SearchUtil.getCourtNamesFromText(title, builder, "法院");
        SearchUtil.getCourtNamesFromText(text, builder, "法院");
        String subText = builder.toString();
        builder = new StringBuilder();
        if (StrUtil.isNotBlank(subText)) {
            for (String str : courtNames) {
                if (subText.contains(str)) {
                    builder.append(str).append(" ");
                    subText = subText.replaceAll(str, "");
                }
            }
        }
        Set<String> hitCourtNames = new LinkedHashSet<>();
        List<String> names = new ArrayList<>(Arrays.asList(builder.toString().split(" ")));
        if (CollUtil.isNotEmpty(areaCodeName)) {
            for (String hitCourtName : names) {
                for (String areaName : areaCodeName) {
                    if (areaName != null && hitCourtName.startsWith(areaName)) {
                        hitCourtNames.add(hitCourtName);
                        break;
                    }
                }
            }
            hitCourtNames.addAll(names);
            bean.setHitCourtNames(CollUtil.join(hitCourtNames, " "));
        } else {
            bean.setHitCourtNames(builder.toString());
        }

        title = HighlightUtil.highlighter(title, keyWord, true);
        text = HighlightUtil.highlighter(text, keyWord, true);
        Set<String> hitWords = HighlightUtil.extractEmTagContents(title + text);
        bean.setHitWords(CollUtil.join(hitWords, " ").replaceAll("<em>", ""));
        if (StrUtil.isBlankIfStr(title)) {
            //标题为空显示作者
            bean.setTitle(bean.getAuthor());
        } else {
            bean.setTitle(title);
        }
        bean.setText(extractSummary(text, 120));
        //短信报送页面需要一个简短的摘要
        bean.setSummary(StrUtil.subWithLength(extractSummary(text, 10), 0, 60));
    }


    private static String extractSummary(String text, int textLenth) {
        //正文标红关键词前后内容摘要截取
        if (text.contains("<em>") && text.indexOf("<em>") > textLenth) {
            String preText = text.substring(0, text.indexOf("<em>"));
            String aftText = text.substring(text.indexOf("<em>"));
            if (preText.contains("/n")) {
                preText = preText.substring(preText.lastIndexOf("/n") + 2);
            }
            if (preText.contains("。")) {
                preText = preText.substring(preText.lastIndexOf("。") + 1);
            }
            if (preText.length() > textLenth && preText.contains("，")) {
                preText = preText.substring(preText.lastIndexOf("，") + 1);
            }
            if (preText.length() > textLenth && preText.contains(" ")) {
                preText = preText.substring(preText.lastIndexOf(" ") + 1);
            }
            if (preText.length() > textLenth) {
                preText = preText.substring(preText.length() - textLenth);
            }
            text = preText + aftText;
        }
        return text;
    }

    public static void main(String[] args) {
        String text = "就这样一家冲刺上市的月子中心，我们在他的官方公众号“圣贝拉母婴护理中心”的一则声明却发现了之前巍阁公司告杭州贝康公司旗下品牌圣贝拉，小贝拉月子中心不实宣传，恶意诋毁案魏阁胜诉！<em>法院</em>对不履行<em>判决</em>的<em>被告</em>及法人限制高消费。圣贝拉的声明2024年7月7日00:31，圣贝拉连夜发文声明不实收购巍阁，ACI母婴护理师资";
        text = extractSummary(text, 20);
        System.out.println(StrUtil.subWithLength(text, 0, 10));
    }

    @Override
    public Integer similarCount(SearchVO searchVO) {
        return EsSearchUtil.getSimilarCount(searchVO);
    }

    @Override
    public Integer realTimeInfoCount(EsSearchBO bo) {
        return EsSearchUtil.realTimeInfoCount(bo);
    }

    @Override
    public JSONObject getSearchCriteria(Long planId) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();
        JSONObject vo = redisTemplate.getCacheObject(RedisConstant.criteria_prefix + userId + ":" + planId);
        if (vo != null) {
            return vo;
        } else {
            return new JSONObject();
        }
    }

    @Override
    public JSONObject getSearchCriteria(Long userId, Long planId) {
        if (userId == null || planId == null) {
            return new JSONObject();
        }
        try {
            return redisTemplate.getCacheObject(RedisConstant.criteria_prefix + userId + ":" + planId);
        } catch (Exception e) {
            log.error("SearchServiceImpl.getSearchCriteria userId: {}. planId: {}, 错误: {}", userId, planId, e.getMessage());
            return new JSONObject();
        }
    }

    @Override
    public void saveSearchCriteria(JSONObject searchVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();
        redisTemplate.setCacheObject(RedisConstant.criteria_prefix + userId + ":" + searchVO.getStr("planId"), searchVO);
    }


    static final String SMS_VERIFY_CODE = "5epIEIbq8XoIQJddSAyOgRxY4RKqlOkS9hi";
    static final String TEMPLATE = "lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABmsC0BL";
    static final String URL = "192.168.10.86:36509/sms/send";

    @Override
    public boolean sendMsg1(MessageVO messageVO) {
//        String url = ShortUrlClientNew.coverUrlToShort(messageVO.getUrl());
//        if (StrUtil.isNotEmpty(url)){
//            messageVO.setUrl(url);
//        }
        for (String phone : messageVO.getPhoneList()) {
//            PushContacts byId = pushContactsService.getById(user);
//            if (null==byId){
//                throw new CustomException("勾选的用户没配置短信!");
//            }
            JSONObject jsonObject = createTemplate(messageVO, phone);
            log.info("发送短信内容：" + jsonObject);
            try {
                HttpResponse execute = HttpUtil.createPost(URL)
                        .body(jsonObject.toJSONString(0))
                        .header("Authorization", SMS_VERIFY_CODE)
                        .execute();
                log.info("发送结果:{}", execute.body());
                return true;
            } catch (Exception e) {
                e.printStackTrace();
                return false;
            }
        }
        return false;
    }

    @Override
    public boolean sendMsg(MessageVO messageVO) {
        Long deptId = messageVO.getDeptId();
        List<String> params = new ArrayList<>(10);
        if (deptId == null) {
            throw new CustomException("参数异常");
        }
        MessageTemplate deptMessageTemplate = messageTemplateService.getDeptMessageTemplate(new DeptMessage(deptId));
        if (deptMessageTemplate == null) {
            throw new CustomException("短信模板配置有误");
        }
        StringBuilder msg = new StringBuilder();
        JSONObject formdata = messageVO.getFormdata();
        String templateScript = deptMessageTemplate.getTemplateScript();
        JSONArray arr = JSONUtil.parseArray(templateScript);
        for (Map.Entry<String, Object> formdatum : formdata) {
            for (Object o : arr) {
                JSONObject form = (JSONObject) o;
                if (form.get("field").equals(formdatum.getKey())) {
                    Object value = formdatum.getValue();
                    Object o1 = form.get("name");
                    msg.append(o1.toString()).append(":").append(value).append(",");
                    params.add(value.toString());
                    if (formdatum.getKey().equals("url")) {
                        messageVO.setUrl(value.toString());//更新url字段值为文章详情，方便回溯
                    }
                    break;
                }
            }
        }
        log.debug("字段内容值是:" + msg);
        boolean specPhone = true;
        if (CollUtil.isEmpty(messageVO.getUserIds())) {
            messageVO.setUserIds(Arrays.asList(messageVO.getPhone()));
            specPhone = false;
        }
        boolean flag = false;
        for (String user : messageVO.getUserIds()) {
            PushContacts byId = null;
            if (specPhone) {
                byId = pushContactsService.getById(user);
                if (null == byId) {
                    throw new CustomException("勾选的用户没配置短信!");
                }
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("type", deptMessageTemplate.getTemplateCode());
            jsonObject.put("phone", specPhone ? byId.getPhone() : messageVO.getPhone());
            for (int i = 0; i < params.size(); i++) {
                jsonObject.set("p" + (i + 1), StrUtil.emptyToDefault(params.get(i), "暂无"));
            }
            log.info("发送短信内容：" + jsonObject);
            try {
                HttpResponse execute = HttpUtil.createPost(URL).body(jsonObject.toJSONString(0)).header("Authorization", SMS_VERIFY_CODE).execute();
                log.info("发送结果:{}", execute.body());
                flag = true;
            } catch (Exception e) {
                e.printStackTrace();
                throw new CustomException("短信发送失败");
            }
        }
        return flag;
    }

    @Override
    public boolean sendWarnMsgDefault(String phone, String p1, String p2, String p3) {
        String type = "lSDAssdAnrNhLnrbFYgDM2R9Ls4d5sasd5ABms0034";
        String authorization = "5epIEIbq8XoIQJddSAyOgRxY4RKqlOkS9hi";
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("type", type);
        jsonObject.set("phone", phone);
        jsonObject.set("p1", p1);
        jsonObject.set("p2", p2);
        jsonObject.set("p3", p3);

        log.info("发送短信内容：" + jsonObject);
        try (HttpResponse execute = HttpUtil.createPost(URL)
                .body(jsonObject.toJSONString(0))
                .header("Authorization", authorization).execute()) {
            if (!execute.isOk()) {
                return false;
            }
            log.info("发送结果:{}", execute.body());
            return true;
        } catch (Exception e) {
            log.error("短信发送失败, 号码; {}", phone);
            return false;
        }
    }

    @Override
    public EsSearchBO buildEsSearchBO(HomeDownVO homeDownVO) {
        String videoHost1 = homeDownVO.getVideoHost();
        try {
            if (!StrUtil.isBlankIfStr(videoHost1)) {
                if (videoHost1.endsWith(",")) {
                    videoHost1 = videoHost1.substring(0, videoHost1.length() - 1);
                    homeDownVO.setVideoHost(videoHost1);
                }
            }
        } catch (Exception e) {
            log.warn("videoHost1逗号去除问题");
        }
        String contentAreaCode = homeDownVO.getContentAreaCode();
        if (CharSequenceUtil.isEmpty(contentAreaCode)) {
            homeDownVO.setContentAreaCode(null);
        }
        //首页媒体
        String homeMedia = homeDownVO.getHomeMedia();
        if (CharSequenceUtil.isNotBlank(homeMedia)) {
            String videoHost;
            switch (homeMedia) {
                case "网站":
                    homeDownVO.setType("1");
                    break;
                case "微信":
                    homeDownVO.setType("5");
                    break;
                case "微博":
                    homeDownVO.setType("3");
                    break;
                case "抖音":
                    homeDownVO.setType("11");
                    videoHost = DictUtils.getDictValue("sys_search_short_video", "抖音");
                    homeDownVO.setVideoHost(videoHost);
                    break;
                case "快手":
                    homeDownVO.setType("11");
                    videoHost = DictUtils.getDictValue("sys_search_short_video", "快手");
                    homeDownVO.setVideoHost(videoHost);
                    break;
                case "今日头条":
                    homeDownVO.setType("11");
                    videoHost = DictUtils.getDictValue("sys_search_short_video", "头条");
                    homeDownVO.setVideoHost(videoHost);
                    break;
                default:
                    break;
            }
        }
        //辖区总览
        String areaOverview = homeDownVO.getAreaOverview();
        if (CharSequenceUtil.isNotBlank(areaOverview)) {
            List<String> areaCodeList = new ArrayList<>();
            List<String> areaOverviewList = CharSequenceUtil.split(areaOverview, ",");
            for (String areaOverviewStr : areaOverviewList) {
                String areaCode = getAreaCode(areaOverviewStr);
                if (CharSequenceUtil.isNotBlank(areaCode)) {
                    areaCodeList.add(areaCode);
                }
            }
            if (CollUtil.isNotEmpty(areaCodeList)) {
                String areaCodeJoin = CollUtil.join(areaCodeList, ",");
                homeDownVO.setContentAreaCode(areaCodeJoin);
            }
        }
        EsSearchBO bo;
        Long planId = homeDownVO.getPlanId();
        Plan plan = planService.selectPlanById(planId);
        if (plan != null) {
            bo = convertHandler.copyPropertiesFromPlan(homeDownVO, plan);
        } else {
            bo = homeDownVO.convertEsSearchBO();
        }
        Integer useCommon = homeDownVO.getUseCommon();
        if (ObjectUtil.equals(useCommon, 1)) {
            HomeStatisServiceImpl.commonQueryFilter2(bo);
        }
        String hotWordCloud = homeDownVO.getHotWordCloud();
        if (CharSequenceUtil.isNotBlank(hotWordCloud)) {
            bo.setKeyWord4(hotWordCloud);
        }
        String author = homeDownVO.getAuthor();
        bo.setAuthor(author);
        bo.setRealCount(1);
        String searchPosition = homeDownVO.getSearchPosition();
        if (!StrUtil.isBlankIfStr(searchPosition)) {
            bo.setSearchPosition(searchPosition);
        }
        return bo;
    }

    @Override
    public EsBeanVO docDetail(EsSearchBO infoVO) {
        String id = infoVO.getId();
        if (id == null) {
            throw new CustomException("id不能为空");
        }
        EsBeanVO resultDocVO = new EsBeanVO();
        try {
            EsSearchBO esSearchBO = new EsSearchBO();
            esSearchBO.setId(id);
            String time = infoVO.getStartTime();
            esSearchBO.setStartTime(time);
            esSearchBO.setEndTime(time);
            EsBean resultDocVO1 = EsSearchUtil.searchByIdTimex(esSearchBO);
            if (resultDocVO1 == null) {
                return null;
            }
            String host = resultDocVO1.getHost();
//            List<AccountInfoVO> accountInfoVOList = elasticsearchService.getNameByHosts(Collections.singletonList(resultDocVO1.getHost()));
            Map<Integer, List<String>> hostMap = (Map<Integer, List<String>>) new HashMap<>().put(resultDocVO1.getType(), Collections.singleton(host));
            List<AccountInfoVO> accountInfoVOList = elasticsearchService.getNameByHosts(hostMap);
            Map<String, String> hostAndNameMap = new HashMap<>();
            for (AccountInfoVO accountInfoVO : accountInfoVOList) {
                hostAndNameMap.putIfAbsent(accountInfoVO.getDomain(), accountInfoVO.getSector());
            }
            List<String> courtNames = hotService.selectCourtNames();
            markKeywords(getSearchKeyWord(infoVO), resultDocVO1, courtNames, hostAndNameMap, new ArrayList<>());
            EsBeanVO bean = JSONUtil.toBean(JSONUtil.toJsonStr(resultDocVO1), EsBeanVO.class);
            bean.setHostName(resultDocVO1.getHost());
            bean.setHost(host);
            return bean;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultDocVO;
    }

    @Override
    public List<Hot> getSimilarHot(SearchVO searchVO) {
        List<Hot> hotList = new ArrayList<>();
        List<Hot> hots = hotListMapper.selectHotAllList(Arrays.asList("WB_REALTIME", "DOUYIN_VIDEO", "TOUTIAO_HOT"));
        Map<String, List<Hot>> collect = hots.stream().collect(Collectors.groupingBy(Hot::getType));
        Set<String> strings = collect.keySet();
        for (String string : strings) {
            List<Hot> hots1 = collect.get(string);
            updateHot(hots1);
            List<String> collect1 = hots1.stream().map(Hot::getTitle).collect(Collectors.toList());
            List<String> similarityList = similarityService.getSimilarityTextList(searchVO.getKeyWord1(), collect1, 0.5);
            for (String b : similarityList) {
                Hot hot = getHot(hots1, b);
                if (null != hot) {
                    hotList.add(hot);
                }
            }
        }
        return hotList;
    }

    @Override
    public PageResult<EsBean> searchSimilarity(EsSearchBO bo) {
        bo.setExcludeWord(null);
        bo.setExcludeId(null);
        bo.setSourceSetting(null);
        bo.setSourceMap(null);
        String keyWord1 = bo.getKeyWord1();
        String keyWord2 = bo.getKeyWord2();
        String keyWord3 = bo.getKeyWord3();
        String keyWord4 = bo.getKeyWord4();
        int configSelect = bo.getConfigSelect();
        String proWord = bo.getProWord();
        bo.setKeyWord1(null);
        bo.setKeyWord2(null);
        bo.setKeyWord3(null);
        bo.setKeyWord4(null);
        bo.setConfigSelect(0);
        PageResult<EsBean> esBeanList = getSimilarityData(bo);
        bo.setKeyWord1(keyWord1);
        bo.setKeyWord2(keyWord2);
        bo.setKeyWord3(keyWord3);
        bo.setKeyWord4(keyWord4);
        bo.setConfigSelect(configSelect);
        bo.setProWord(proWord);
        searchResProcess(bo, esBeanList);
        return esBeanList;
    }

    public PageResult<EsBean> getSimilarityData(EsSearchBO bo) {
        PageResult<EsBean> esBeanList = new PageResult<>();
        JSONObject resultJson = EsSearchUtil.getPostBodyJSONObject(bo, EsSearchUtil.SEARCH_SIMILARITY);
        JSONObject o = (JSONObject) resultJson.get("data");
        if (null != o && o.containsKey("records")) {
            List<EsBean> list = new ArrayList<>(JSONUtil.toList(o.getJSONArray("records"), EsBean.class));
            if (CollUtil.isNotEmpty(list)) {
                esBeanList.addAll(list);
                esBeanList.setTotal(o.getInt("total"));
            }
        }
        return esBeanList;
    }

    @Override
    public JSONArray accountLevelCount(EsSearchBO bo) {
        Map<String, Integer> map = EsSearchUtil.mediaTypeCountForOriginal(bo);
        SysDictData dictData = new SysDictData();
        dictData.setDictType("sys_search_account_level");
        dictData.setStatus("0");
        List<String> types = dictDataService.selectDictDataList(dictData).stream().map(SysDictData::getDictValue).collect(Collectors.toList());
        JSONArray array = new JSONArray();
        JSONObject object;
        for (String type : types) {
            object = JSONUtil.createObj();
            object.putOnce("name", type);
            int value = 0;
            for (String s : type.split(",")) {
                value += map.getOrDefault(s, 0);
            }
            object.putOnce("value", value);
            array.set(object);
        }
        return array;
    }

    @Override
    public Integer getRealTimeInfoCount(SearchVO searchVO) {
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
        return realTimeInfoCount(bo);
    }

    private void updateHot(List<Hot> hots1) {
        for (int i = 0; i < hots1.size(); i++) {
            Hot hot = hots1.get(i);
            hot.setSort(Long.valueOf(String.valueOf(i + 1)));
            String type = hot.getType();
            if ("WB_REALTIME".equals(type)) {
                hot.setType("微博实时上升热点");
            } else if ("DOUYIN_VIDEO".equals(type)) {
                hot.setType("抖音热搜榜");
            } else if ("TOUTIAO_HOT".equals(type)) {
                hot.setType("头条热搜榜");
            }
        }
    }

    public Hot getHot(List<Hot> hot, String text) {
        for (Hot hot1 : hot) {
            if (hot1.getTitle().equals(text)) {
                return hot1;
            }
        }
        return null;
    }

    @Override
    public JSONArray mediaTypeCount(EsSearchBO bo) {
        Map<String, Integer> map = EsSearchUtil.mediaTypeCountForOriginal(bo);
        String[] types = bo.getType().split(",");
        JSONArray array = new JSONArray();
        JSONObject object;
        for (String type : types) {
            object = JSONUtil.createObj();
            object.putOnce("name", type);
            object.putOnce("value", map.getOrDefault(type, 0));
            array.set(object);
        }
        return array;
    }

    public String getAreaCode(String code) {
//        Map<String, String> maps = new HashMap<>();
//        maps.put("博约舆情", "330000");
//        maps.put("杭州中院", "330100");
//        maps.put("宁波中院", "330200");
//        maps.put("温州中院", "330300");
//        maps.put("嘉兴中院", "330400");
//        maps.put("湖州中院", "330500");
//        maps.put("绍兴中院", "330600");
//        maps.put("金华中院", "330700");
//        maps.put("衢州中院", "330800");
//        maps.put("舟山中院", "330900");
//        maps.put("台州中院", "331000");
//        maps.put("丽水中院", "331100");
        return String.valueOf(COURT_NAME_CODE_MAPS.get(code));
    }

    /**
     * 构建短信模板
     *
     * @param phone
     * @return
     */
    private static JSONObject createTemplate(MessageVO messageVO, String phone) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("type", SearchServiceImpl.TEMPLATE);
        jsonObject.put("phone", phone);

        jsonObject.set("p1", StrUtil.emptyToDefault(messageVO.getCourtName(), "暂无"));

        jsonObject.set("p2", StrUtil.emptyToDefault(messageVO.getRiskLevel(), "暂无"));

        jsonObject.set("p3", StrUtil.emptyToDefault(messageVO.getSource(), "暂无"));

        jsonObject.set("p4", StrUtil.emptyToDefault(messageVO.getSummary(), "暂无"));

        jsonObject.set("p5", StrUtil.emptyToDefault(messageVO.getUrl(), "暂无"));
        return jsonObject;
    }

    private void cacheState(String id, String url) {
        String key = RedisConstant.info_status_prefix + id;
        if (!redisTemplate.expire(key, 60, TimeUnit.MINUTES)) {
            JSONObject jsonObject = UrlAccessUtil.getUrlState(url);
            redisTemplate.setCacheObject(key, jsonObject, 60, TimeUnit.MINUTES);
        }
    }

    @Override
    public JSONObject eventContext(EsSearchBO searchVO) {
        long start = System.currentTimeMillis();
        JSONObject res = EsSearchUtil.eventContext(searchVO);
        long end = System.currentTimeMillis();
        double passedTime = (end - start) / 1000.0;
        if (passedTime > 0.2) {
            log.warn("博约舆情pt-serv纯搜索耗时{}秒,业务耗时:{}秒", passedTime);
        }
        return res;
    }

    public List<EsBean> getFirstRelease(SearchVO query) {
        List<EsBean> list = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(query.getPlanId())) {
            Plan plan = planService.selectPlanById(query.getPlanId());
            // 舆情监测
            if (plan != null) {
                SearchVO searchVO = new SearchVO();
                EsSearchBO bo = convertHandler.copyPropertiesFromPlan(searchVO, plan);
                if (plan.getHistoryFlag() == 1) {
                    bo.setStartTime(DateUtil.formatDateTime(plan.getEffectStartTime()));
                    bo.setEndTime(DateUtil.formatDateTime(plan.getEffectEndTime()));
                    bo.setIndexs(Arrays.asList(NETXMAN_YQ_HISPLAN));
                } else {
                    bo.setEndTime(DateUtil.formatDateTime(new Date()));
                    bo.setStartTime(DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -30)));
                    bo.setIndexs(Arrays.asList(ES_INDEX_WEEK, ES_INDEX_MONTH));
                }
                bo.setSortType(getEnumValue(SortTypeEnum.TIME_ASC));
                list = EsSearchUtil.firstRelease(bo);
            }
        }
        // 全文搜索
        else {
            EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(query);
            list = EsSearchUtil.firstRelease(bo);
        }

        // 翻译 host
        Map<Integer, List<String>> map = new HashMap<>();
        list.forEach(x -> {
            if (StringUtils.isEmpty(x.getHost())) {
                if (StringUtils.isNotEmpty(x.getUrl())) {
                    x.setHost(StringUtils.getHostFromUrl(x.getUrl()));
                }
            }
            if (StringUtils.isEmpty(x.getTitle())) {
                x.setTitle(x.getText().substring(0, 20));
            }
            List<String> values = map.getOrDefault(x.getType(), new ArrayList<>());
            if (!values.contains(x.getHost())) {
                values.add(x.getHost());
            }
            map.put(x.getType(), values);
        });
        List<AccountInfoVO> accountInfoVOList = elasticsearchService.getNameByHosts(map);

        list.forEach(x -> {
            Optional<AccountInfoVO> first = accountInfoVOList.stream().filter(y -> y.getDomain().equals(x.getHost())).findFirst();
            String hostName = "";
            if (first.isPresent()) {
                hostName = first.get().getSector();
            }
            if (StringUtils.isEmpty(hostName)) {
                x.setHost(x.getHost());
            } else {
                x.setHost(hostName);
            }
        });

        // list.stream().sorted(Comparator.comparing(EsBean::getPublishTime)).collect(Collectors.toList())
        return list;
    }

    @Override
    public List<EsBean> relatedHotArticle(SearchVO query) {
        List<EsBean> list = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(query.getPlanId())) {
            Plan plan = planService.selectPlanById(query.getPlanId());
            if (plan != null) {
                SearchVO searchVO = new SearchVO();
                EsSearchBO bo = convertHandler.copyPropertiesFromPlan(searchVO, plan);
                list = EsSearchUtil.relatedHotArticle(bo);
            }
        }
        return list;
    }
}
