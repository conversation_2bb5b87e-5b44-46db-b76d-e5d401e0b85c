package com.boryou.web.module.warn.task;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.system.OsInfo;
import cn.hutool.system.SystemUtil;
import com.boryou.common.core.domain.entity.SysDept;
import com.boryou.system.mapper.SysDeptMapper;
import com.boryou.web.constant.NoticeEnum;
import com.boryou.web.constant.RedisConstant;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.controller.common.util.ConvertHandler;
import com.boryou.web.domain.Plan;
import com.boryou.web.domain.vo.SearchVO;
import com.boryou.web.module.mail.service.MailService;
import com.boryou.web.module.notice.domain.ByNotice;
import com.boryou.web.module.notice.service.IByNoticeService;
import com.boryou.web.module.stream.producer.AfterWarnProducer;
import com.boryou.web.module.warn.domain.vo.ContactUserVO;
import com.boryou.web.module.warn.domain.vo.WarnDataVO;
import com.boryou.web.module.warn.domain.vo.WarnSMVO;
import com.boryou.web.module.warn.domain.vo.WarnSetVO;
import com.boryou.web.module.warn.service.ShortUrlApiService;
import com.boryou.web.module.warn.service.WarnDataService;
import com.boryou.web.module.warn.service.WarnSetService;
import com.boryou.web.module.wechat.service.WxTemplateService;
import com.boryou.web.service.HolidayService;
import com.boryou.web.service.PlanService;
import com.boryou.web.service.SearchService;
import com.boryou.web.service.impl.SearchServiceImpl;
import com.boryou.web.util.HighlightUtil;
import com.boryou.web.util.OurMd5Util;
import com.boryou.web.util.RedisLock;
import com.boryou.web.util.SMUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Component
@EnableScheduling
public class WarnPushTask {

    private final WarnSetService warnSetService;
    private final PlanService planService;
    private final HolidayService holidayService;
    private final WarnDataService warnDataService;
    private final ConvertHandler convertHandler;
    private final MailService mailService;
    private final WxTemplateService wxTemplateService;
    private final SearchService searchService;
    private final ShortUrlApiService shortUrlApiService;
    private final IByNoticeService noticeService;
    private final SysDeptMapper sysDeptMapper;
    private final AfterWarnProducer afterWarnProducer;
    private final RedisLock redisLock;

    @Value("${runWarn}")
    private Boolean runWarn = false;
    @Value("${warnHost}")
    private String warnHost = "http://************:8399";
    @Value("${wx.templateId}")
    private String templateId = "zeGgmn_Q9mAVjhhkUrVZ9mqOIkeh-7So3LCcvTDS1H4";

    /**
     * 1.定时任务每10秒运行一次
     * 2.获取所有预警设置
     * 3.对单条设置使用planId查询es最后预警时间, 若定时预警, 判断时间间隔, 若实时预警, 不需要判断
     * 4.使用预警设置查询es, 获取预警数据.(查询200条, 博约舆情如此)
     * 5.批量判断预警数据是否已推送, 若未推送, 推送预警数据
     */
    @Scheduled(fixedDelay = 5 * 60 * 1000)
    public void push() {
        OsInfo osInfo = SystemUtil.getOsInfo();
        if (osInfo.isWindows()) {
            return;
        }
        String pushKey = RedisConstant.LOCK_KEY + "push";
        redisLock.executeWithLock4j(pushKey, () -> {
            log.warn("外层开始预警地址: {}", SystemUtil.getHostInfo().getAddress());
            this.warnPush();
            log.warn("外层预警推送完成");
        });
    }

    private void warnPush() {
        log.warn("开始预警, runWarn: {}", runWarn);
        if (Boolean.FALSE.equals(runWarn)) {
            return;
        }

        List<Long> planIdList = planService.allOpenPlanId();
        if (CollUtil.isEmpty(planIdList)) {
            log.warn("没有开启的未过期的不是历史的方案");
            return;
        }

        List<WarnSetVO> allOpenWarnSet = warnSetService.getAllOpenWarnSet(planIdList);
        if (CollUtil.isEmpty(allOpenWarnSet)) {
            log.warn("没有开启的预警设置");
            return;
        }

        for (WarnSetVO warnSetVO : allOpenWarnSet) {
            try {
                DateTime date = DateUtil.date();
                //DateTime date = DateUtil.parse("2024-07-18 16:00:00");
                //DateTime date = DateUtil.offsetDay(DateUtil.date(), -4);
                String day = DateUtil.format(date, "yyyy-MM-dd");
                String time = DateUtil.format(date, "HH:mm");
                boolean holiday = holidayService.isHoliday(day);
                if (!this.judgeAcceptTime(warnSetVO, holiday) ||
                        !this.judgeReceiveTime(warnSetVO, time) ||
                        !this.judgeContact(warnSetVO)) {
                    continue;
                }
                DateTime lastPushTime = this.judgePushTime(warnSetVO, date);
                if (lastPushTime == null) {
                    continue;
                }
                realPush(warnSetVO, date, lastPushTime);
            } catch (Exception e) {
                log.warn("预警失败: {}, 预警设置为: {}", e.getMessage(), warnSetVO);
            }
        }
        log.warn("预警推送完成");
    }

    private DateTime judgePushTime(WarnSetVO warnSetVO, DateTime date) {
        Integer warningType = warnSetVO.getWarningType();
        if (warningType == null || (warningType != 1 && warningType != 0)) {
            return null;
        }
        Date lastPushTime = warnSetVO.getLastPushTime();
        if (lastPushTime != null) {
            if (lastPushTime.getTime() > date.getTime()) {
                return null;
            }
            if (warningType == 0) {
                //定时
                Integer intervalTime = warnSetVO.getIntervalTime();
                long betweenMinute = DateUtil.between(lastPushTime, date, DateUnit.MINUTE);
                if (betweenMinute < intervalTime) {
                    //没到间隔时段内
                    return null;
                }
            }
        }
        //始终查询8小时前的数据,避免爬虫,刷盘等
        return DateUtil.offsetHour(date, -8);
    }

    private boolean judgeContact(WarnSetVO warnSetVO) {
        Integer message = warnSetVO.getMessage();
        Integer mail = warnSetVO.getMail();
        Integer chat = warnSetVO.getChat();
        Integer system = warnSetVO.getSystem();
        List<ContactUserVO> messageContact = warnSetVO.getMessageContact();
        List<ContactUserVO> mailContact = warnSetVO.getMailContact();
        List<ContactUserVO> chatContact = warnSetVO.getChatContact();
        return (message != null && message == 1 && CollUtil.isNotEmpty(messageContact)) ||
                (mail != null && mail == 1 && CollUtil.isNotEmpty(mailContact)) ||
                (chat != null && chat == 1 && CollUtil.isNotEmpty(chatContact)) ||
                (system != null && system == 1);
    }

    private void realPush(WarnSetVO warnSetVO, DateTime date, Date lastPushTime) {
        Long planId = warnSetVO.getPlanId();
        Plan plan = planService.selectPlanById(planId);
        if (plan == null) {
            return;
        }
        Long userId = warnSetVO.getUserId();
        JSONObject jsonObject = searchService.getSearchCriteria(userId, planId);
        SearchVO searchVO = this.buildWarnSearchVO(jsonObject);
        searchVO.setUserId(userId);
        EsSearchBO esSearchBO = convertHandler.copyPropertiesFromPlan(searchVO, plan);

        String startTime = DateUtil.format(lastPushTime, "yyyy-MM-dd HH:mm:ss");
        String endTime = DateUtil.format(date, "yyyy-MM-dd HH:mm:ss");
        String type = warnSetVO.getType();
        String emotionFlag = warnSetVO.getEmotionFlag();
        String searchPosition = warnSetVO.getSearchPosition();
        String isOriginal = warnSetVO.getIsOriginal();
        String kw1 = warnSetVO.getKw1();
        String excludeWord = warnSetVO.getExcludeWord();

        esSearchBO.setStartTime(startTime);
        esSearchBO.setEndTime(endTime);
        esSearchBO.setType(type);
        esSearchBO.setEmotionFlag(emotionFlag);
        esSearchBO.setSearchPosition(searchPosition);
        esSearchBO.setIsOriginal(CharSequenceUtil.equals(isOriginal, "1"));
        esSearchBO.setQuadraticWord(kw1);
        esSearchBO.setQuadraticFilterWord(excludeWord);

        List<EsBean> pageResult = warnDataService.warnDataPush(esSearchBO);
        if (CollUtil.isEmpty(pageResult)) {
            return;
        }
        if (CharSequenceUtil.equals(isOriginal, "1")) {
            //在代码使用MD5去重
            pageResult = pageResult.stream()
                    .collect(Collectors.toMap(
                            EsBean::getMd5, // 键是md5
                            item -> item, // 值是record本身
                            // 合并函数，保留time较晚的record
                            (existing, replacement) -> existing.getPublishTime().after(replacement.getPublishTime()) ? existing : replacement
                    ))
                    .values() // 获取值的集合
                    .stream() // 对values集合创建stream
                    .sorted(Comparator.comparing(EsBean::getPublishTime)) // 根据时间排序
                    .collect(Collectors.toList()); // 转换回List
        }

        //与已预警去重
        List<String> repeatArticleId = new ArrayList<>();
        List<String> articleIdList = pageResult.stream().map(EsBean::getId).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(articleIdList)) {
            List<List<String>> split = CollUtil.split(articleIdList, 5000);
            for (List<String> articleId5000 : split) {
                WarnDataVO warnDataVO = new WarnDataVO();
                warnDataVO.setPlanId(String.valueOf(planId));
                warnDataVO.setArticleIds(articleId5000);
                List<String> repeatArticleIdList = warnDataService.warnDataCheck(warnDataVO);
                if (CollUtil.isNotEmpty(repeatArticleIdList)) {
                    repeatArticleId.addAll(repeatArticleIdList);
                }
            }
        }

        Long deptId = warnSetVO.getDeptId();
        List<String> users = new ArrayList<>();
        users.add(String.valueOf(userId));
        String keyWord = SearchServiceImpl.getSearchKeyWord(esSearchBO);
        if (!StrUtil.isBlankIfStr(kw1)) {
            keyWord = keyWord + " " + kw1;
        }

        List<EsBean> warnDataResList = new ArrayList<>();

        //预警级别
        Map<String, Integer> grading = warnSetVO.getGrading();
        for (EsBean esBean : pageResult) {
            String articleId = esBean.getId();
            if (repeatArticleId.contains(articleId)) {
                //去重复
                continue;
            }
            EsBean warnDataRes = BeanUtil.copyProperties(esBean, EsBean.class);
            String title = esBean.getTitle();
            String text = esBean.getText();
            String title1 = HighlightUtil.highlighter(title, keyWord, true);
            String text2 = HighlightUtil.highlighter(text, keyWord, true);
            Set<String> hitWordsSet = HighlightUtil.extractEmTagContents(title1 + text2);
            List<String> hitWords = new ArrayList<>();
            for (String hitWord : hitWordsSet) {
                hitWords.add(hitWord.replace("<em>", ""));
            }
            Integer warnType = 1;

            warnDataRes.setId(String.valueOf(OurMd5Util.getMd5(articleId + planId + userId)));
            warnDataRes.setPlanId(String.valueOf(planId));
            warnDataRes.setArticleId(articleId);
            warnDataRes.setUserId(String.valueOf(userId));
            warnDataRes.setDeptId(String.valueOf(deptId));
            warnDataRes.setUsers(users);
            warnDataRes.setHitWord(hitWords);
            warnDataRes.setWarnType(warnType);
            warnDataRes.setWarnTime(endTime);
            Integer accountGrade = warnDataRes.getAccountGrade();
            String accountGradeStr = String.valueOf(accountGrade);
            if (grading.containsKey(accountGradeStr)) {
                Integer i = grading.get(accountGradeStr);
                warnDataRes.setWarnGrade(i);
            } else {
                warnDataRes.setWarnGrade(1);
            }
            warnDataResList.add(warnDataRes);
        }
        if (CollUtil.isEmpty(warnDataResList)) {
            return;
        }
        List<Integer> pushTypeList = this.getPushType(warnSetVO);
        if (CollUtil.isEmpty(pushTypeList)) {
            return;
        }
        for (EsBean esBean : warnDataResList) {
            esBean.setPushType(pushTypeList);
        }
        boolean warned = false;
        boolean b1 = warnDataService.warnDataAdd(warnDataResList);
        //更新上次预警时间
        Long id = warnSetVO.getId();
        boolean b2 = warnSetService.updateLastPushTime(id, date);
        if (b1 && b2) {
            int total = warnDataResList.size();
            String planName = plan.getPlanName();
            //详情页待更新 todo
            WarnSMVO warnSMVO = new WarnSMVO();
            warnSMVO.setDate(endTime);
            warnSMVO.setPlanId(String.valueOf(planId));
            warnSMVO.setDeptId(deptId);
            warnSMVO.setUsers(users);
            String encrypt = SMUtil.encryptHex(JSONUtil.toJsonStr(warnSMVO));
            //String url = "https://justice.boryou.com/zjgy/warnList?p=" + encrypt;
            String system = "boryou";
            SysDept sysDept = sysDeptMapper.selectDeptById(deptId);
            if (sysDept != null) {
                String systemId = sysDept.getSystemId();
                if (CharSequenceUtil.isNotBlank(systemId)) {
                    system = systemId;
                }
            }
            String url = warnHost + "/" + system + "/warnList?p=" + encrypt;
            //开始推送
            Integer message = warnSetVO.getMessage();
            if (message != null && message == 1) {
                List<ContactUserVO> messageContact = warnSetVO.getMessageContact();
                if (CollUtil.isNotEmpty(messageContact)) {
                    String messageUrl = shortUrlApiService.getShortUrl(url);
                    if (StrUtil.isBlankIfStr(messageUrl)) {
                        messageUrl = url;
                    }
                    for (ContactUserVO contactUserVO : messageContact) {
                        //String username = contactUserVO.getUsername();
                        String phone = contactUserVO.getPhone();
                        //String content = "预警方案: " + planName + " 发现" + (total) + "条信息";
                        searchService.sendWarnMsgDefault(phone, String.valueOf(total), planName, messageUrl);
                        //发送失败处理 todo
                    }
                    warned = true;
                }
            }
            Integer mail = warnSetVO.getMail();
            if (mail != null && mail == 1) {
                List<ContactUserVO> messageContact = warnSetVO.getMailContact();
                if (CollUtil.isNotEmpty(messageContact)) {
                    for (ContactUserVO contactUserVO : messageContact) {
                        String username = contactUserVO.getUsername();
                        String email = contactUserVO.getEmail();
                        mailService.sendWarnMail(email, username, planName, "舆情预警", total, url);
                        //发送失败处理 todo
                    }
                    warned = true;
                }
            }
            Integer chat = warnSetVO.getChat();
            if (chat != null && chat == 1) {
                List<ContactUserVO> chatContact = warnSetVO.getChatContact();
                if (CollUtil.isNotEmpty(chatContact)) {
                    //String first = "从" + startTime + "到" + endTime + "，监测方案【" + planName + "】新增" + total + "条信息。";
                    //String first = "【" + planName + "】方案: 新增" + total + "条预警信息";
                    String first = planName;
                    //随机3条
                    //int max = total - 1;
                    //Set<Integer> randomNonRepeatingNumbers = this.getRandomNonRepeatingNumbers(0, max, max < 3 ? max : 3);
                    //StringBuilder stringBuilder = new StringBuilder();
                    //for (Integer randomNonRepeatingNumber : randomNonRepeatingNumbers) {
                    //    EsBean esBean = warnDataResList.get(randomNonRepeatingNumber);
                    //    String title = esBean.getTitle();
                    //    String text = esBean.getText();
                    //    if (CharSequenceUtil.isBlank(title)) {
                    //        stringBuilder.append(text.length() > 30 ? text.substring(0, 30) + "..." : text).append("\n");
                    //    } else {
                    //        stringBuilder.append(title.length() > 30 ? title.substring(0, 30) + "..." : title).append("\n");
                    //    }
                    //}
                    //String keyword1 = stringBuilder.toString();
                    String keyword1 = "";
                    for (ContactUserVO contactUserVO : chatContact) {
                        String username = contactUserVO.getUsername();
                        String wxOpenId = contactUserVO.getWxOpenId();
                        wxTemplateService.sendTemplateMessage(wxOpenId, templateId, first, keyword1, url);
                        //发送失败处理 todo
                    }
                    warned = true;
                }
            }
            Integer systemPush = warnSetVO.getSystem();
            if (systemPush != null && systemPush == 1) {
                warnNotice(warnDataResList, users);
            }
        } else {
            log.error("预警推送失败id: {}", warnSetVO.getId());
        }
//        if (warned) {
//            for (EsBean esBean : warnDataResList) {
////                try {
////                    EsSpecialData esSpecialData = new EsSpecialData();
////                    esSpecialData.setMd5(esBean.getMd5());
////                    esSpecialData.setIndexId(Long.parseLong(esBean.getId()));
////                    esSpecialData.setSubmit(1);
////                    esSpecialData.setCreateBy("admin");
////                    int i = esSpecialDataService.updateSearchData(esSpecialData);
//////                    log.info("更新预警信息成功，预警:{}", i > 0);
////                } catch (Exception e) {
////                    e.printStackTrace();
////                }
////                处理时间太长卡住预警, 使用redis消息队列异步处理
//                WarnAfterVO warnAfterVO = new WarnAfterVO();
//                warnAfterVO.setM(esBean.getMd5());
//                warnAfterVO.setI(Long.parseLong(esBean.getId()));
//                warnAfterVO.setS(1);
//                warnAfterVO.setC("admin");
//                afterWarnProducer.sendMsg(warnAfterVO);
//            }
//        }


    }

    private SearchVO buildWarnSearchVO(JSONObject jsonObject) {
        try {
            if (Objects.isNull(jsonObject) || jsonObject.equals(new JSONObject())) {
                return new SearchVO();
            }
            SearchVO bean = JSONUtil.toBean(jsonObject, SearchVO.class);
            List<String> contentFormList = jsonObject.getBeanList("contentForm", String.class);
            if (CollUtil.isNotEmpty(contentFormList)) {
                bean.setContentForm(CollUtil.join(contentFormList, ","));
            } else {
                bean.setContentForm(null);
            }
            List<String> forwardList = jsonObject.getBeanList("forward", String.class);
            if (CollUtil.isNotEmpty(forwardList)) {
                bean.setForward(CollUtil.join(forwardList, ","));
            } else {
                bean.setForward(null);
            }
            List<String> typeList = jsonObject.getBeanList("type", String.class);
            if (CollUtil.isNotEmpty(typeList)) {
                bean.setType(CollUtil.join(typeList, ","));
            } else {
                bean.setType(null);
            }
            List<String> videoHostList = jsonObject.getBeanList("videoHost", String.class);
            if (CollUtil.isNotEmpty(videoHostList)) {
                bean.setVideoHost(CollUtil.join(videoHostList, ","));
            } else {
                bean.setVideoHost(null);
            }
            return bean;
        } catch (Exception e) {
            log.error("buildWarnSearchVO传参: {}, 错误: {}", jsonObject, e.getMessage());
            return new SearchVO();
        }
    }

    private void warnNotice(List<EsBean> warnDataResList, List<String> users) {
        if (CollUtil.isEmpty(warnDataResList)) {
            return;
        }
        //只通知5条,全部取舆情预警列表页查看
        List<EsBean> sub = CollUtil.sub(warnDataResList, 0, 5);
        if (CollUtil.isEmpty(sub)) {
            return;
        }
        for (EsBean esBean : sub) {
            String articleId = esBean.getArticleId();
            String id = esBean.getId();
            Date publishTime = esBean.getPublishTime();
            ByNotice byNotice = new ByNotice();
            byNotice.setNoticeType(NoticeEnum.WARN.getType());
            byNotice.setNoticeTime(new Date());
            //预警需要单独查询
            byNotice.setDocIndexId(Long.valueOf(id));
            byNotice.setTime(publishTime);
            noticeService.saveWarnNotice(byNotice, users);
        }

    }

    private List<Integer> getPushType(WarnSetVO warnSetVO) {
        List<Integer> pushTypeList = new ArrayList<>();
        Integer message = warnSetVO.getMessage();
        List<ContactUserVO> messageContact = warnSetVO.getMessageContact();
        if (message != null && message == 1 && CollUtil.isNotEmpty(messageContact)) {
            pushTypeList.add(1);
        }
        Integer mail = warnSetVO.getMail();
        List<ContactUserVO> mailContact = warnSetVO.getMailContact();
        if (mail != null && mail == 1 && CollUtil.isNotEmpty(mailContact)) {
            pushTypeList.add(2);
        }
        Integer chat = warnSetVO.getChat();
        List<ContactUserVO> chatContact = warnSetVO.getChatContact();
        if (chat != null && chat == 1 && CollUtil.isNotEmpty(chatContact)) {
            pushTypeList.add(3);
        }
        Integer system = warnSetVO.getSystem();
        if (system != null && system == 1) {
            pushTypeList.add(4);
        }
        return pushTypeList;
    }

    /**
     * 获取指定范围内指定数量的随机且不重复的数
     *
     * @param min   范围最小值
     * @param max   范围最大值
     * @param count 生成的随机数数量
     * @return 包含不重复随机数的Set
     */
    public Set<Integer> getRandomNonRepeatingNumbers(int min, int max, int count) {
        if ((max - min + 1) < count) {
            throw new IllegalArgumentException("请求的随机数数量超出了可能的范围");
        }

        Set<Integer> randomNumbers = new HashSet<>();

        // 当集合中数字数量少于所需数量时，继续生成新的随机数
        while (randomNumbers.size() < count) {
            randomNumbers.add(RandomUtil.randomInt(min, max + 1));
        }

        return randomNumbers;
    }

    /**
     * 判断是否在接收时间内 接收时间(0: 每天 1: 工作日 2: 节假日)
     */
    private boolean judgeAcceptTime(WarnSetVO warnSetVO, boolean holiday) {
        Integer acceptTime = warnSetVO.getAcceptTime();
        if (acceptTime == null) {
            return false;
        }
        if (acceptTime == 0) {
            return true;
        }
        if (acceptTime == 1) {
            return !holiday;
        } else if (acceptTime == 2) {
            return holiday;
        } else {
            return false;
        }

    }

    private boolean judgeReceiveTime(WarnSetVO warnSetVO, String time) {
        String startReceiveTime = warnSetVO.getStartReceiveTime();
        String endReceiveTime = warnSetVO.getEndReceiveTime();
        if (CharSequenceUtil.equals("00:00", endReceiveTime)) {
            endReceiveTime = "23:59";
        }
        return CharSequenceUtil.compare(startReceiveTime, time, true) <= 0 &&
                CharSequenceUtil.compare(endReceiveTime, time, true) >= 0;
    }


}
