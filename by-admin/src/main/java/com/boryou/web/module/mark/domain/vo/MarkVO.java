package com.boryou.web.module.mark.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class MarkVO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * ES Bean Mark ID
     */
    private String esBeanMarkId;

    /**
     * 文章id
     */
    @NotBlank(message = "文章ID不能为空")
    private String articleId;

    /**
     * 媒体类型
     */
    private Integer type;

    /**
     * 媒体类型名称
     */
    private String typeName;

    /**
     * 发文时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publishTime;

    /**
     * 标题
     */
    private String title;

    /**
     * 正文
     */
    private String text;

    /**
     * 摘要，非es字段
     */
    private String summary;

    /**
     * 原文链接
     */
    private String url;

    /**
     * host
     */
    private String host;

    /**
     * 域名
     */
    private String domain;

    /**
     * (账号/作者)昵称
     */
    private String author;

    /**
     * (账号/作者)id
     */
    private String authorId;

    /**
     * 作者性别(0:女,1:男)
     */
    private Integer authorSex;

    /**
     * 平台业务ID
     */
    private String bizId;

    /**
     * 账号级别 (0达人  1蓝v  2红v  3橙v  4普通用户)
     */
    private Integer accountLevel;

    /**
     * 站点地域(码)
     */
    private String siteAreaCode;

    /**
     * 站点地域
     */
    private String siteAreaCodeName;

    /**
     * 内容地域(码)
     */
    private List<String> contentAreaCode;

    /**
     * 内容地域
     */
    private String contentAreaCodeName;

    /**
     * 站点标签
     */
    private List<String> siteMeta;

    /**
     * 内容标签
     */
    private List<String> contentMeta;

    /**
     * 粉丝数
     */
    private Integer fansNum;

    /**
     * 阅读数
     */
    private Integer readNum;

    /**
     * 评论数
     */
    private Integer commentNum;

    /**
     * 点赞数
     */
    private Integer likeNum;

    /**
     * 转发数
     */
    private Integer reprintNum;

    /**
     * 所属板块
     */
    private String sector;

    /**
     * 内容形式
     */
    private List<Integer> contentForm;

    /**
     * 内容MD5
     */
    private String md5;

    /**
     * 网页源码路径
     */
    private String srcCodePath;

    /**
     * 封面图片链接
     */
    private String coverUrl;

    /**
     * 图片链接数组
     */
    private List<String> picUrl;

    /**
     * 音视频链接数组
     */
    private List<String> avdUrl;

    /**
     * 情感标识
     */
    private Integer emotionFlag;

    /**
     * 是否为原创
     */
    private Boolean isOriginal;

    /**
     * 是否被标记为垃圾内容
     */
    private Boolean isSpam;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 发布日期
     */
    private String day;

    /**
     * 是否已读
     */
    private Integer isRead;

    /**
     * 命中词
     */
    private String hitWords;

    /**
     * status 1在  0不在(删除)
     */
    private Integer status;

    /**
     * 处置 0无 1已处置
     */
    private Integer deal;

    /**
     * 关注 0无 1已关注
     */
    private Integer follow;

    /**
     * 信源级别
     */
    private Integer accountGrade;

    /**
     * 预警 0无 1已预警
     */
    private Integer warned;

    /**
     * 是否推送过短信，邮箱,微信 0 无 1 有
     */
    private Integer submits;

    /**
     * 命中关键词 (系统预警)
     */
    private List<String> hitWord;

    /**
     * 方案id
     */
    private String planId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 信息浏览 0:未读 1:已读
     */
    private Integer readFlag;

    /**
     * 用户列表
     */
    private List<String> users;

    /**
     * 分页参数
     */
    private Integer pageNum;
    private Integer pageSize;
}
