-- 根据 EsBeanMark 生成的 MySQL 建表语句
-- List 类型使用 JSON 存储

CREATE TABLE `by_mark` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `es_bean_mark_id` VARCHAR(64) DEFAULT NULL COMMENT 'ES Bean Mark ID',
    `article_id` VARCHAR(64) DEFAULT NULL COMMENT '文章ID',
    `type` INT(11) DEFAULT NULL COMMENT '媒体类型',
    `type_name` VARCHAR(100) DEFAULT NULL COMMENT '媒体类型名称',
    `publish_time` DATETIME DEFAULT NULL COMMENT '发文时间',
    `title` TEXT DEFAULT NULL COMMENT '标题',
    `text` LONGTEXT DEFAULT NULL COMMENT '正文',
    `summary` TEXT DEFAULT NULL COMMENT '摘要，非es字段',
    `url` TEXT DEFAULT NULL COMMENT '原文链接',
    `host` VARCHAR(255) DEFAULT NULL COMMENT 'host',
    `domain` VARCHAR(255) DEFAULT NULL COMMENT '域名',
    `author` VARCHAR(255) DEFAULT NULL COMMENT '(账号/作者)昵称',
    `author_id` VARCHAR(64) DEFAULT NULL COMMENT '(账号/作者)id',
    `author_sex` INT(11) DEFAULT NULL COMMENT '作者性别(0:女,1:男)',
    `biz_id` VARCHAR(64) DEFAULT NULL COMMENT '平台业务ID',
    `account_level` INT(11) DEFAULT NULL COMMENT '账号级别 (0达人 1蓝v 2红v 3橙v 4普通用户)',
    `site_area_code` VARCHAR(64) DEFAULT NULL COMMENT '站点地域(码)',
    `site_area_code_name` VARCHAR(255) DEFAULT NULL COMMENT '站点地域',
    `content_area_code` JSON DEFAULT NULL COMMENT '内容地域(码) - List<String>',
    `content_area_code_name` VARCHAR(255) DEFAULT NULL COMMENT '内容地域',
    `site_meta` JSON DEFAULT NULL COMMENT '站点标签 - List<String>',
    `content_meta` JSON DEFAULT NULL COMMENT '内容标签 - List<String>',
    `fans_num` INT(11) DEFAULT NULL COMMENT '粉丝数',
    `read_num` INT(11) DEFAULT NULL COMMENT '阅读数',
    `comment_num` INT(11) DEFAULT NULL COMMENT '评论数',
    `like_num` INT(11) DEFAULT NULL COMMENT '点赞数',
    `reprint_num` INT(11) DEFAULT NULL COMMENT '转发数',
    `sector` VARCHAR(255) DEFAULT NULL COMMENT '所属板块',
    `content_form` JSON DEFAULT NULL COMMENT '内容形式 - List<Integer>',
    `md5` VARCHAR(64) DEFAULT NULL COMMENT '内容MD5',
    `src_code_path` VARCHAR(500) DEFAULT NULL COMMENT '网页源码路径',
    `cover_url` TEXT DEFAULT NULL COMMENT '封面图片链接',
    `pic_url` JSON DEFAULT NULL COMMENT '图片链接数组 - List<String>',
    `avd_url` JSON DEFAULT NULL COMMENT '音视频链接数组 - List<String>',
    `emotion_flag` INT(11) DEFAULT NULL COMMENT '情感标识',
    `is_original` TINYINT(1) DEFAULT NULL COMMENT '是否为原创',
    `is_spam` TINYINT(1) DEFAULT NULL COMMENT '是否被标记为垃圾内容',
    `update_time` DATETIME DEFAULT NULL COMMENT '更新时间',
    `day` VARCHAR(20) DEFAULT NULL COMMENT '发布日期',
    `is_read` INT(11) DEFAULT NULL COMMENT '是否已读',
    `hit_words` TEXT DEFAULT NULL COMMENT '命中词',
    `status` INT(11) DEFAULT 1 COMMENT 'status 1在 0不在(删除)',
    `deal` INT(11) DEFAULT 0 COMMENT '处置 0无 1已处置',
    `follow` INT(11) DEFAULT 0 COMMENT '关注 0无 1已关注',
    `account_grade` INT(11) DEFAULT NULL COMMENT '信源级别',
    `warned` INT(11) DEFAULT 0 COMMENT '预警 0无 1已预警',
    `submits` INT(11) DEFAULT 0 COMMENT '是否推送过短信，邮箱,微信 0 无 1 有',
    `hit_word` JSON DEFAULT NULL COMMENT '命中关键词 (系统预警) - List<String>',
    `plan_id` VARCHAR(64) DEFAULT NULL COMMENT '方案id',
    `user_id` VARCHAR(64) DEFAULT NULL COMMENT '用户id',
    `dept_id` VARCHAR(64) DEFAULT NULL COMMENT '部门id',
    `read_flag` INT(11) DEFAULT 0 COMMENT '信息浏览 0:未读 1:已读',
    `users` JSON DEFAULT NULL COMMENT '用户列表 - List<String>',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` VARCHAR(64) DEFAULT NULL COMMENT '创建者',
    `update_by` VARCHAR(64) DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标记数据表';
